import { IsNotEmpty, <PERSON><PERSON><PERSON><PERSON>, IsOptional, IsString, IsUUID } from "class-validator";
import { EActivityStatus } from "../enum/EActivityStatus";

export namespace CreateActivityDTO {
  export class Input {
    @IsString()
    @IsNotEmpty()
    title: string;

    @IsString()
    @IsNotEmpty()
    description: string;

    @IsString()
    @IsNotEmpty()
    date: string;

    @IsUUID()
    @IsNotEmpty()
    villageId: string;

    @IsOptional()
    tasks?: Task[];
  }

  class Task {
    @IsUUID()
    @IsOptional()
    id?: string;

    @IsString()
    @IsNotEmpty()
    title: string;

    @IsString()
    @IsNotEmpty()
    description: string;

    @IsString()
    @IsNotEmpty()
    isiboId: string;

    @IsNumber()
    @IsOptional()
    estimatedCost?: number;

    @IsNumber()
    @IsOptional()
    actualCost?: number;

    @IsNumber()
    @IsOptional()
    expectedParticipants?: number;

    @IsNumber()
    @IsOptional()
    actualParticipants?: number;

    @IsNumber()
    @IsOptional()
    totalEstimatedCost?: number;

    @IsNumber()
    @IsOptional()
    totalActualCost?: number;

    @IsNumber()
    @IsOptional()
    expectedFinancialImpact?: number;

    @IsNumber()
    @IsOptional()
    actualFinancialImpact?: number;
  }

  export class Output {
    id: string;
    title: string;
    description: string;
    status: EActivityStatus;
    date: Date;
    tasks: Task[];
  }
}
