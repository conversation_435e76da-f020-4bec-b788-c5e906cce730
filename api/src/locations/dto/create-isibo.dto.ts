import { Is<PERSON>rray, IsNot<PERSON>mpty, <PERSON><PERSON><PERSON>al, <PERSON>String, <PERSON>U<PERSON><PERSON> } from "class-validator";

export namespace CreateIsiboDto {
  export class Input {
    @IsString()
    @IsNotEmpty()
    name: string;

    @IsUUID()
    @IsOptional()
    leaderId?: string;

    @IsUUID()
    @IsNotEmpty()
    villageId: string;

    @IsArray()
    @IsUUID("4", { each: true })
    @IsOptional()
    memberIds?: string[];
  }
}
