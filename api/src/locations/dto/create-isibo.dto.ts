import { IsNotEmpty, <PERSON><PERSON><PERSON>al, <PERSON><PERSON>tring, IsUUI<PERSON> } from "class-validator";
import { Citizen } from "../entities/citizen.entity";

export namespace CreateIsiboDto {
  export class Input {
    @IsString()
    @IsNotEmpty()
    name: string;

    @IsUUID()
    @IsOptional()
    leaderId?: string;

    @IsUUID()
    @IsNotEmpty()
    villageId: string;

    @IsNotEmpty()
    members: Citizen[];
  }
}
