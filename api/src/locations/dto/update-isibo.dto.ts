import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>Optional, IsString, IsUUID } from "class-validator";

export namespace UpdateIsiboDto {
  export class Input {
    @IsString()
    @IsOptional()
    name?: string;

    @IsUUID()
    @IsOptional()
    leaderId?: string;

    @IsUUID()
    @IsOptional()
    villageId?: string;

    @IsArray()
    @IsUUID("4", { each: true })
    @IsOptional()
    memberIds?: string[];
  }
}
