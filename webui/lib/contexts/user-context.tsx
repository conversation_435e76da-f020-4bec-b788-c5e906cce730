"use client";

import { getProfile, User } from "@/lib/api/users";
import React, { createContext, useContext, useEffect, useState } from "react";
import { usePathname } from "next/navigation";

interface UserContextType {
  user: User | null;
  isLoading: boolean;
  error: Error | null;
  refreshUser: () => Promise<void>;
  clearUser: () => void;
  hasValidToken: () => boolean;
  fetchUserForPage: (pageName: string) => Promise<void>;
  manualRefresh: () => Promise<void>;
}

const UserContext = createContext<UserContextType | undefined>(undefined);

export function UserProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);
  const pathname = usePathname();

  // Pages that are allowed to fetch fresh user data
  const FETCH_ALLOWED_PAGES = ['/dashboard', '/profile'];

  // Check if current page is allowed to fetch user data
  const canFetchOnCurrentPage = () => {
    return FETCH_ALLOWED_PAGES.some(page => pathname?.startsWith(page));
  };

  // Check if there's a valid token in localStorage
  const hasValidToken = (): boolean => {
    try {
      const token = localStorage.getItem("access_token");
      return !!token;
    } catch {
      return false;
    }
  };

  // Load user from localStorage without API call
  const loadUserFromStorage = () => {
    try {
      const storedUser = localStorage.getItem("user");
      if (storedUser) {
        const parsedUser = JSON.parse(storedUser);
        setUser(parsedUser);
        console.log("📦 User loaded from localStorage");
        return true;
      }
    } catch (err) {
      console.error("❌ Failed to load user from localStorage:", err);
    }
    return false;
  };

  // Fetch user profile from API (only for allowed pages)
  const fetchUserProfile = async (forceFetch = false) => {
    // Only fetch if on allowed page or forced
    if (!forceFetch && !canFetchOnCurrentPage()) {
      console.log(`🚫 Page ${pathname} not allowed to fetch user data, using stored data`);
      loadUserFromStorage();
      return;
    }

    try {
      setIsLoading(true);
      setError(null);
      console.log(`📡 Fetching user profile for page: ${pathname}`);

      // Fetch fresh data from the server
      const profile = await getProfile();

      // Update state and localStorage
      setUser(profile);
      localStorage.setItem("user", JSON.stringify(profile));
      console.log("✅ User profile fetched successfully");
    } catch (err) {
      console.error("❌ Failed to fetch user profile:", err);
      setError(
        err instanceof Error ? err : new Error("Failed to fetch user profile")
      );

      // If we have a stored user, keep using it even if the refresh failed
      loadUserFromStorage();
    } finally {
      setIsLoading(false);
    }
  };

  // Function to refresh user profile (force fetch) - DISABLED to prevent infinite loop
  const refreshUser = async () => {
    console.log("🔄 refreshUser() called - DISABLED to prevent infinite loop");
    console.log("📦 Loading user from storage instead");
    loadUserFromStorage();
  };

  // Function to fetch user for specific page - DISABLED to prevent infinite loop
  const fetchUserForPage = async (pageName: string) => {
    console.log(`🔄 fetchUserForPage() called for: ${pageName} - DISABLED to prevent infinite loop`);
    console.log("📦 Loading user from storage instead");
    loadUserFromStorage();
  };

  // Function to manually refresh user profile (for explicit use only)
  const manualRefresh = async () => {
    console.log("🔄 manualRefresh() called - FORCE FETCHING");
    await fetchUserProfile(true);
  };

  // Function to clear user profile (for logout)
  const clearUser = () => {
    setUser(null);
    localStorage.removeItem("user");
    localStorage.removeItem("access_token");
    localStorage.removeItem("refresh_token");
    console.log("🧹 User data cleared");
  };

  // Initialize user context on mount ONLY
  useEffect(() => {
    console.log(`🚀 UserContext initializing`);

    // Always try to load from localStorage first
    loadUserFromStorage();

    // Note: Individual pages will call fetchUserForPage() when they need fresh data
  }, []); // Only run once on mount

  return (
    <UserContext.Provider
      value={{
        user,
        isLoading,
        error,
        refreshUser,
        clearUser,
        hasValidToken,
        fetchUserForPage,
        manualRefresh,
      }}
    >
      {children}
    </UserContext.Provider>
  );
}

// Custom hook to use the user context
export function useUser() {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error("useUser must be used within a UserProvider");
  }
  return context;
}
