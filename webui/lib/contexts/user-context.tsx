"use client";

import { getProfile, User } from "@/lib/api/users";
import React, { createContext, useContext, useEffect, useRef, useState } from "react";

// Global circuit breaker to prevent infinite loops across all instances
class GlobalUserFetchCircuitBreaker {
  private static instance: GlobalUserFetchCircuitBreaker;
  private callCount = 0;
  private lastCallTime = 0;
  private isTripped = false;
  private isRefreshing = false;

  static getInstance(): GlobalUserFetchCircuitBreaker {
    if (!GlobalUserFetchCircuitBreaker.instance) {
      GlobalUserFetchCircuitBreaker.instance = new GlobalUserFetchCircuitBreaker();
    }
    return GlobalUserFetchCircuitBreaker.instance;
  }

  canFetch(): boolean {
    const now = Date.now();

    // Reset counter after 10 seconds
    if (now - this.lastCallTime > 10000) {
      this.callCount = 0;
      this.isTripped = false;
    }

    // Check if already refreshing
    if (this.isRefreshing) {
      console.warn("⚠️ User profile fetch already in progress globally, skipping...");
      return false;
    }

    // Check circuit breaker
    if (this.isTripped) {
      console.error("🚫 Global circuit breaker is tripped, not fetching user profile");
      return false;
    }

    // Increment call count
    this.callCount++;
    this.lastCallTime = now;

    // Trip breaker if too many calls
    if (this.callCount > 3) {
      this.isTripped = true;
      console.error("🚨 GLOBAL CIRCUIT BREAKER TRIPPED: Too many user profile calls detected!");
      console.error("🔍 This indicates an infinite loop in user fetching");
      console.error("🛑 All user fetching is now BLOCKED to prevent infinite loop");
      return false;
    }

    console.log(`📡 Global user fetch allowed (call #${this.callCount})`);
    return true;
  }

  startFetch(): void {
    this.isRefreshing = true;
  }

  endFetch(): void {
    this.isRefreshing = false;
  }

  reset(): void {
    this.callCount = 0;
    this.isTripped = false;
    this.isRefreshing = false;
    console.log("🔄 Global circuit breaker reset");
  }
}

const globalCircuitBreaker = GlobalUserFetchCircuitBreaker.getInstance();

interface UserContextType {
  user: User | null;
  isLoading: boolean;
  error: Error | null;
  refreshUser: () => Promise<void>;
  clearUser: () => void;
  resetCircuitBreaker: () => void;
}

const UserContext = createContext<UserContextType | undefined>(undefined);

export function UserProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);

  // Function to fetch user profile with global circuit breaker
  const fetchUserProfile = async () => {
    // Check global circuit breaker
    if (!globalCircuitBreaker.canFetch()) {
      return;
    }

    try {
      globalCircuitBreaker.startFetch();
      setIsLoading(true);
      setError(null);

      // Always fetch fresh data from the server
      const profile = await getProfile();

      // Update state and localStorage
      setUser(profile);
      localStorage.setItem("user", JSON.stringify(profile));
      console.log("✅ User profile fetched successfully");
    } catch (err) {
      console.error("❌ Failed to fetch user profile:", err);
      setError(
        err instanceof Error ? err : new Error("Failed to fetch user profile")
      );

      // If we have a stored user, keep using it even if the refresh failed
      const storedUser = localStorage.getItem("user");
      if (storedUser) {
        setUser(JSON.parse(storedUser));
      }
    } finally {
      setIsLoading(false);
      globalCircuitBreaker.endFetch();
    }
  };

  // Function to refresh user profile
  const refreshUser = async () => {
    console.log("🔄 refreshUser() called");
    console.trace("📍 Call stack for refreshUser:");
    await fetchUserProfile();
  };

  // Function to clear user profile (for logout)
  const clearUser = () => {
    setUser(null);
    localStorage.removeItem("user");
    globalCircuitBreaker.endFetch();
  };

  // Function to reset circuit breaker (for debugging)
  const resetCircuitBreaker = () => {
    globalCircuitBreaker.reset();
  };

  // Load user from localStorage on mount ONLY
  useEffect(() => {
    const storedUser = localStorage.getItem("user");
    if (storedUser) {
      try {
        setUser(JSON.parse(storedUser));
      } catch (e) {
        console.error("Failed to parse stored user:", e);
        localStorage.removeItem("user");
      }
    }
    setIsLoading(false);
  }, []);

  // We're removing the pathname-based refresh to avoid infinite loops

  return (
    <UserContext.Provider
      value={{
        user,
        isLoading,
        error,
        refreshUser,
        clearUser,
        resetCircuitBreaker,
      }}
    >
      {children}
    </UserContext.Provider>
  );
}

// Custom hook to use the user context
export function useUser() {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error("useUser must be used within a UserProvider");
  }
  return context;
}
