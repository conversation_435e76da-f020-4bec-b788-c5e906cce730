import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import { Toaster } from "sonner";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Communiserver",
  description: "Communiserver platform",
};

// Import the providers
import { UserInitializer } from "@/components/user-initializer";
import { AuthProvider } from "@/contexts/auth-context";
import { UserProvider } from "@/lib/contexts/user-context";

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <AuthProvider>
          <UserProvider>
            <UserInitializer />
            {children}
          </UserProvider>
        </AuthProvider>
        <Toaster />
      </body>
    </html>
  );
}
