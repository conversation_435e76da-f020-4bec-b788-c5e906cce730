import { NextRequest, NextResponse } from "next/server";

// API endpoint URL
const API_URL = process.env.API_URL || "http://localhost:8000";

// Global call counter to detect infinite loops
let callCount = 0;
let lastResetTime = Date.now();

export async function GET(request: NextRequest) {
  try {
    // Reset counter every 10 seconds
    const now = Date.now();
    if (now - lastResetTime > 10000) {
      callCount = 0;
      lastResetTime = now;
    }

    callCount++;
    console.log(`🚨 /api/v1/users/me called ${callCount} times in last 10 seconds`);

    // If more than 5 calls in 10 seconds, block and log details
    if (callCount > 5) {
      console.error("🚨 INFINITE LOOP DETECTED IN /api/v1/users/me");
      console.error("🔍 Call count:", callCount);
      console.error("🔍 User-Agent:", request.headers.get("User-Agent"));
      console.error("🔍 Referer:", request.headers.get("Referer"));

      // Return a special response to identify the source
      return NextResponse.json(
        {
          error: "INFINITE_LOOP_DETECTED",
          message: "Too many calls to /api/v1/users/me detected",
          callCount: callCount
        },
        { status: 429 }
      );
    }

    // Get the access token from the request headers
    const authHeader = request.headers.get("Authorization");

    if (!authHeader) {
      return NextResponse.json(
        { message: "Authorization token is required" },
        { status: 401 }
      );
    }

    // Forward the request to the backend API
    const response = await fetch(`${API_URL}/api/v1/users/me`, {
      method: "GET",
      headers: {
        "Authorization": authHeader,
        "Content-Type": "application/json",
      },
    });

    const data = await response.json();

    // Return the response from the backend
    return NextResponse.json(data, { status: response.status });
  } catch (error) {
    console.error("Get profile API error:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
