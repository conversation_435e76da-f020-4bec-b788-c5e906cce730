import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest) {
  try {
    // Return mock villages data for public use
    const mockVillages = {
      message: "Villages retrieved successfully",
      payload: [
        {
          id: "village-1",
          name: "KIMISAGARA",
          hasLeader: true,
          leaderId: "leader-1",
          cell: {
            id: "cell-1",
            name: "NYARUGENGE"
          }
        },
        {
          id: "village-2",
          name: "KACYIRU",
          hasLeader: true,
          leaderId: "leader-2",
          cell: {
            id: "cell-2",
            name: "<PERSON><PERSON>Y<PERSON><PERSON>"
          }
        },
        {
          id: "village-3",
          name: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
          hasLeader: true,
          leaderId: "leader-3",
          cell: {
            id: "cell-3",
            name: "<PERSON><PERSON><PERSON><PERSON><PERSON>"
          }
        },
        {
          id: "village-4",
          name: "<PERSON><PERSON><PERSON><PERSON>",
          hasLeader: true,
          leaderId: "leader-4",
          cell: {
            id: "cell-4",
            name: "REMER<PERSON>"
          }
        },
        {
          id: "village-5",
          name: "KIMIHURURA",
          hasLeader: true,
          leaderId: "leader-5",
          cell: {
            id: "cell-5",
            name: "KIMIHURURA"
          }
        },
        {
          id: "village-6",
          name: "GISOZ<PERSON>",
          hasLeader: true,
          leaderId: "leader-6",
          cell: {
            id: "cell-6",
            name: "GISOZI"
          }
        },
        {
          id: "village-7",
          name: "NIBOYE",
          hasLeader: true,
          leaderId: "leader-7",
          cell: {
            id: "cell-7",
            name: "NIBOYE"
          }
        },
        {
          id: "village-8",
          name: "KAGARAMA",
          hasLeader: true,
          leaderId: "leader-8",
          cell: {
            id: "cell-8",
            name: "KAGARAMA"
          }
        }
      ]
    };

    return NextResponse.json(mockVillages);
  } catch (error) {
    console.error("Error in public villages route handler:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
