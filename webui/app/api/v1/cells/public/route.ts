import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest) {
  try {
    // Return mock cells data for public use
    const mockCells = {
      message: "Cells retrieved successfully",
      payload: [
        {
          id: "cell-1",
          name: "NYARUGENGE",
          hasLeader: true,
          leaderId: "leader-1",
          sector: {
            id: "sector-1",
            name: "NYARUGENGE"
          }
        },
        {
          id: "cell-2",
          name: "KACYIRU",
          hasLeader: true,
          leaderId: "leader-2",
          sector: {
            id: "sector-2",
            name: "<PERSON><PERSON><PERSON><PERSON><PERSON>"
          }
        },
        {
          id: "cell-3",
          name: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
          hasLeader: true,
          leaderId: "leader-3",
          sector: {
            id: "sector-3",
            name: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"
          }
        },
        {
          id: "cell-4",
          name: "R<PERSON>ER<PERSON>",
          hasLeader: true,
          leaderId: "leader-4",
          sector: {
            id: "sector-4",
            name: "REMER<PERSON>"
          }
        },
        {
          id: "cell-5",
          name: "KIMIHURURA",
          hasLeader: true,
          leaderId: "leader-5",
          sector: {
            id: "sector-5",
            name: "KIMIHURURA"
          }
        },
        {
          id: "cell-6",
          name: "GISOZI",
          hasLeader: true,
          leaderId: "leader-6",
          sector: {
            id: "sector-6",
            name: "GISOZI"
          }
        },
        {
          id: "cell-7",
          name: "NIBOYE",
          hasLeader: true,
          leaderId: "leader-7",
          sector: {
            id: "sector-7",
            name: "NIBOYE"
          }
        },
        {
          id: "cell-8",
          name: "KAGARAMA",
          hasLeader: true,
          leaderId: "leader-8",
          sector: {
            id: "sector-8",
            name: "KAGARAMA"
          }
        }
      ]
    };

    return NextResponse.json(mockCells);
  } catch (error) {
    console.error("Error in public cells route handler:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
